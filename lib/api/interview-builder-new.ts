import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ersion<PERSON>ew,
  Question,
  QuestionType,
  CreateInterviewNewRequest,
  UpdateInterviewNewRequest,
  CreateQuestionNewRequest,
  UpdateQuestionNewRequest,
  InterviewWithLatestVersion,
  InterviewListResponse,
  QuestionListResponse,
  ValidationErrors,
  QuestionValidationResult,
  InterviewValidationResult,
} from '@/types/interview-builder-new';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';

// Generate the Amplify data client
const client = generateClient<Schema>();

// Helper functions for head question logic
export const calculateHeadQuestions = (questions: Question[]): Question[] => {
  // Get all question IDs that are referenced as conditional questions (nextQuestionId in options)
  const conditionalQuestionIds = new Set<string>();

  questions.forEach(question => {
    question.options?.forEach(option => {
      if (option.nextQuestionId) {
        conditionalQuestionIds.add(option.nextQuestionId);
      }
    });
  });

  // Update isHeadQuestion for all questions
  return questions.map(question => ({
    ...question,
    isHeadQuestion: !conditionalQuestionIds.has(question.questionId),
  }));
};

export const calculateDefaultNextQuestionIds = (
  questions: Question[]
): Question[] => {
  // Sort questions by order
  const sortedQuestions = [...questions].sort((a, b) => a.order - b.order);

  // Get head questions only
  const headQuestions = sortedQuestions.filter(q => q.isHeadQuestion);

  return sortedQuestions.map(question => {
    if (!question.isHeadQuestion) {
      // Conditional questions inherit defaultNextQuestionId from their parent
      const parentQuestion = questions.find(q =>
        q.options?.some(option => option.nextQuestionId === question.questionId)
      );
      return {
        ...question,
        defaultNextQuestionId:
          parentQuestion?.defaultNextQuestionId || undefined,
      };
    }

    // Find the next head question for head questions
    const currentIndex = headQuestions.findIndex(
      q => q.questionId === question.questionId
    );
    const nextHeadQuestion = headQuestions[currentIndex + 1];

    return {
      ...question,
      defaultNextQuestionId: nextHeadQuestion?.questionId || undefined,
    };
  });
};

// Validation functions
export const validateInterviewData = (data: any): ValidationErrors => {
  const errors: ValidationErrors = {};

  if (!data.name?.trim()) {
    errors.name = 'Interview name is required';
  }

  if (data.name && data.name.length > 100) {
    errors.name = 'Interview name must be less than 100 characters';
  }

  if (data.description && data.description.length > 500) {
    errors.description = 'Description must be less than 500 characters';
  }

  return errors;
};

export const validateQuestionData = (data: any): ValidationErrors => {
  const errors: ValidationErrors = {};

  if (!data.text?.trim()) {
    errors.text = 'Question text is required';
  }

  if (!data.type) {
    errors.type = 'Question type is required';
  }

  if (['radio', 'select', 'checkbox'].includes(data.type)) {
    if (!data.options || data.options.length === 0) {
      errors.options = 'At least one option is required for this question type';
    } else {
      const hasEmptyOptions = data.options.some(
        (opt: any) => !opt.label?.trim() || !opt.value?.trim()
      );
      if (hasEmptyOptions) {
        errors.options = 'All options must have both label and value';
      }
    }
  }

  return errors;
};

// Interview CRUD operations
export const getInterviewsList = async (): Promise<
  InterviewWithLatestVersion[]
> => {
  try {
    const { data: interviews, errors } = await client.models.Interview.list({
      selectionSet: [
        'id',
        'name',
        'description',
        'isActive',
        'createdAt',
        'updatedAt',
        'versions.id',
        'versions.versionNumber',
        'versions.questions.*',
      ],
    });

    if (errors) {
      console.error('Failed to fetch interviews:', errors);
      return [];
    }

    const interviewsWithLatestVersion = interviews.map(interview => {
      const versions = interview.versions ?? [];

      // Find the latest version
      const latestVersion = versions.reduce<(typeof versions)[0] | null>(
        (latest, version) => {
          return !latest ||
            (version.versionNumber &&
              (!latest.versionNumber ||
                version.versionNumber > latest.versionNumber))
            ? version
            : latest;
        },
        null
      );

      // Count questions from custom type array
      let questionsCount = 0;
      if (latestVersion?.questions && Array.isArray(latestVersion.questions)) {
        questionsCount = latestVersion.questions.filter(q => q != null).length;
      }

      return {
        id: interview.id,
        name: interview.name,
        description: interview.description || undefined,
        isActive: interview.isActive ?? true,
        createdAt: interview.createdAt ?? new Date().toISOString(),
        updatedAt: interview.updatedAt ?? new Date().toISOString(),
        latestVersionNumber: latestVersion?.versionNumber ?? undefined,
        questionsCount,
      };
    });

    return interviewsWithLatestVersion;
  } catch (error) {
    console.error('Error fetching interviews:', error);
    return [];
  }
};

export const getInterviewWithVersion = async (
  interviewId: string
): Promise<{
  interview: InterviewNew | null;
  version: InterviewVersionNew | null;
}> => {
  try {
    const { data: interview, errors } = await client.models.Interview.get(
      { id: interviewId },
      {
        selectionSet: [
          'id',
          'name',
          'description',
          'isActive',
          'createdAt',
          'updatedAt',
          'versions.id',
          'versions.versionNumber',
          'versions.isActive',
          'versions.createdAt',
          'versions.questions.*',
        ],
      }
    );

    if (errors || !interview) {
      console.error('Failed to fetch interview:', errors);
      return { interview: null, version: null };
    }

    // Find the latest active version
    const versions = interview.versions ?? [];
    const latestVersion = versions
      .filter(v => v.isActive)
      .reduce<(typeof versions)[0] | null>((latest, version) => {
        return !latest ||
          (version.versionNumber &&
            (!latest.versionNumber ||
              version.versionNumber > latest.versionNumber))
          ? version
          : latest;
      }, null);

    let parsedQuestions: Question[] = [];
    if (latestVersion?.questions && Array.isArray(latestVersion.questions)) {
      // Convert from database custom type format to our Question type
      parsedQuestions = latestVersion.questions
        .filter(q => q != null)
        .map((q: any) => ({
          questionId: q.questionId,
          text: q.text,
          type: q.type as QuestionType,
          options: q.options
            ? q.options.map((optStr: string) => {
                try {
                  return JSON.parse(optStr);
                } catch {
                  return { id: '', label: optStr, value: optStr };
                }
              })
            : [],
          order: q.order,
          conditionalLogic: q.conditionalLogic || [],
          defaultNextQuestionId: q.defaultNextQuestionId || undefined,
          isHeadQuestion: q.isHeadQuestion ?? true, // Default to true for backward compatibility
          questionMapping: q.questionMapping || undefined,
        }));

      // Recalculate head questions and default next question IDs to ensure consistency
      parsedQuestions = calculateHeadQuestions(parsedQuestions);
      parsedQuestions = calculateDefaultNextQuestionIds(parsedQuestions);
    }

    const interviewData: InterviewNew = {
      id: interview.id,
      name: interview.name,
      description: interview.description || undefined,
      isActive: interview.isActive ?? true,
      createdAt: interview.createdAt ?? new Date().toISOString(),
      updatedAt: interview.updatedAt ?? new Date().toISOString(),
    };

    const versionData: InterviewVersionNew | null = latestVersion
      ? {
          id: latestVersion.id,
          interviewId: interview.id,
          versionNumber: latestVersion.versionNumber ?? 1,
          isActive: latestVersion.isActive ?? true,
          createdAt: latestVersion.createdAt ?? new Date().toISOString(),
          questions: parsedQuestions,
        }
      : null;

    return { interview: interviewData, version: versionData };
  } catch (error) {
    console.error('Error fetching interview with version:', error);
    return { interview: null, version: null };
  }
};

export const createInterview = async (
  data: CreateInterviewNewRequest
): Promise<InterviewNew> => {
  try {
    const now = new Date().toISOString();

    // Create the interview
    const { data: newInterview, errors: interviewErrors } =
      await client.models.Interview.create({
        name: data.name,
        description: data.description,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });

    if (interviewErrors || !newInterview) {
      console.error('Errors creating interview:', interviewErrors);
      throw new Error('Failed to create interview');
    }

    // Create the first version with empty questions
    const { data: newVersion, errors: versionErrors } =
      await client.models.InterviewVersion.create({
        interviewId: newInterview.id,
        versionNumber: 1,
        isActive: true,
        createdAt: now,
        questions: [],
      });

    if (versionErrors) {
      console.error('Errors creating initial version:', versionErrors);
      // Continue even if version creation fails
    }

    return {
      id: newInterview.id,
      name: newInterview.name,
      description: newInterview.description ?? undefined,
      isActive: newInterview.isActive ?? true,
      createdAt: newInterview.createdAt ?? now,
      updatedAt: newInterview.updatedAt ?? now,
    };
  } catch (error) {
    console.error('Error creating interview:', error);
    throw error;
  }
};

export const updateInterview = async (
  data: UpdateInterviewNewRequest
): Promise<InterviewNew> => {
  try {
    const now = new Date().toISOString();

    const { data: updatedInterview, errors } =
      await client.models.Interview.update({
        id: data.id,
        name: data.name,
        description: data.description,
        updatedAt: now,
      });

    if (errors || !updatedInterview) {
      console.error('Errors updating interview:', errors);
      throw new Error('Failed to update interview');
    }

    return {
      id: updatedInterview.id,
      name: updatedInterview.name,
      description: updatedInterview.description ?? undefined,
      isActive: updatedInterview.isActive ?? true,
      createdAt: updatedInterview.createdAt ?? new Date().toISOString(),
      updatedAt: updatedInterview.updatedAt ?? now,
    };
  } catch (error) {
    console.error('Error updating interview:', error);
    throw error;
  }
};

export const deleteInterview = async (
  interviewId: string
): Promise<boolean> => {
  try {
    const { errors } = await client.models.Interview.delete({
      id: interviewId,
    });

    if (errors) {
      console.error('Errors deleting interview:', errors);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error deleting interview:', error);
    return false;
  }
};

// Question management functions
export const createQuestion = async (
  interviewId: string,
  data: CreateQuestionNewRequest
): Promise<Question> => {
  try {
    // Get the current version
    const { interview, version } = await getInterviewWithVersion(interviewId);

    if (!interview || !version) {
      throw new Error('Interview or version not found');
    }

    // Generate new question ID
    const questionId = `q_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Calculate next order
    const nextOrder =
      version.questions.length > 0
        ? Math.max(...version.questions.map(q => q.order)) + 1
        : 1;

    // Create new question object
    const newQuestion: Question = {
      questionId,
      text: data.text,
      type: data.type,
      options:
        data.options?.map((opt, index) => ({
          id: `opt_${Date.now()}_${index}`,
          label: opt.label,
          value: opt.value,
          nextQuestionId: (opt as any).nextQuestionId,
        })) || [],
      order: nextOrder,
      conditionalLogic: data.conditionalLogic || [],
      defaultNextQuestionId: data.defaultNextQuestionId,
      isHeadQuestion: true, // Will be recalculated automatically
      questionMapping: data.questionMapping,
    };

    // Add new question to existing questions and recalculate head question logic
    const allQuestions = [...version.questions, newQuestion];
    const updatedQuestions = calculateHeadQuestions(allQuestions);
    const finalQuestions = calculateDefaultNextQuestionIds(updatedQuestions);

    // Convert to database format
    const questionsForDB = finalQuestions.map(q => ({
      questionId: q.questionId,
      text: q.text,
      type: q.type as any,
      options: q.options?.map(opt => JSON.stringify(opt)) || [],
      order: q.order,
      conditionalLogic: (q.conditionalLogic || []) as any,
      defaultNextQuestionId: q.defaultNextQuestionId || '',
      isHeadQuestion: q.isHeadQuestion ?? true,
      questionMapping: q.questionMapping || '',
    }));

    // Use the calculated questions for database

    // Create new version with updated questions
    const now = new Date().toISOString();
    const newVersionNumber = version.versionNumber + 1;

    const { data: newVersion, errors } =
      await client.models.InterviewVersion.create({
        interviewId,
        versionNumber: newVersionNumber,
        isActive: true,
        createdAt: now,
        questions: questionsForDB,
      });

    if (errors || !newVersion) {
      console.error('Errors creating new version with question:', errors);
      throw new Error('Failed to create question');
    }

    // Deactivate previous version
    await client.models.InterviewVersion.update({
      id: version.id,
      isActive: false,
    });

    // Return the final question with calculated values
    const finalQuestion = finalQuestions.find(q => q.questionId === questionId);
    if (!finalQuestion) {
      throw new Error('Failed to create question');
    }

    return finalQuestion;
  } catch (error) {
    console.error('Error creating question:', error);
    throw error;
  }
};

export const updateQuestion = async (
  interviewId: string,
  data: UpdateQuestionNewRequest
): Promise<Question> => {
  try {
    // Get the current version
    const { interview, version } = await getInterviewWithVersion(interviewId);

    if (!interview || !version) {
      throw new Error('Interview or version not found');
    }

    // Find and update the question
    const questionIndex = version.questions.findIndex(
      q => q.questionId === data.questionId
    );
    if (questionIndex === -1) {
      throw new Error('Question not found');
    }

    // Create updated question object
    const updatedQuestion: Question = {
      questionId: data.questionId,
      text: data.text,
      type: data.type,
      options:
        data.options?.map(opt => ({
          id: (opt as any).id || `opt_${Date.now()}_${Math.random()}`,
          label: opt.label,
          value: opt.value,
          nextQuestionId: (opt as any).nextQuestionId,
        })) || [],
      order: version.questions[questionIndex].order,
      conditionalLogic: data.conditionalLogic || [],
      defaultNextQuestionId: data.defaultNextQuestionId,
      isHeadQuestion: true, // Will be recalculated automatically
      questionMapping: data.questionMapping,
    };

    // Update questions array and recalculate head question logic
    const allQuestions = version.questions.map((q, index) => {
      if (index === questionIndex) {
        return updatedQuestion;
      }
      return q;
    });

    const questionsWithHeadLogic = calculateHeadQuestions(allQuestions);
    const finalQuestions = calculateDefaultNextQuestionIds(
      questionsWithHeadLogic
    );

    // Convert to database format
    const questionsForDB = finalQuestions.map(q => ({
      questionId: q.questionId,
      text: q.text,
      type: q.type as any,
      options: q.options?.map(opt => JSON.stringify(opt)) || [],
      order: q.order,
      conditionalLogic: (q.conditionalLogic || []) as any,
      defaultNextQuestionId: q.defaultNextQuestionId || '',
      isHeadQuestion: q.isHeadQuestion ?? true,
      questionMapping: q.questionMapping || '',
    }));

    // Create new version with updated questions
    const now = new Date().toISOString();
    const newVersionNumber = version.versionNumber + 1;

    const { data: newVersion, errors } =
      await client.models.InterviewVersion.create({
        interviewId,
        versionNumber: newVersionNumber,
        isActive: true,
        createdAt: now,
        questions: questionsForDB,
      });

    if (errors || !newVersion) {
      console.error(
        'Errors creating new version with updated question:',
        errors
      );
      throw new Error('Failed to update question');
    }

    // Deactivate previous version
    await client.models.InterviewVersion.update({
      id: version.id,
      isActive: false,
    });

    // Return the final updated question with calculated values
    const finalQuestion = finalQuestions.find(
      q => q.questionId === data.questionId
    );
    if (!finalQuestion) {
      throw new Error('Failed to update question');
    }

    return finalQuestion;
  } catch (error) {
    console.error('Error updating question:', error);
    throw error;
  }
};

export const deleteQuestion = async (
  interviewId: string,
  questionId: string
): Promise<boolean> => {
  try {
    // Get the current version
    const { interview, version } = await getInterviewWithVersion(interviewId);

    if (!interview || !version) {
      throw new Error('Interview or version not found');
    }

    // Remove the question and recalculate head question logic
    const filteredQuestions = version.questions
      .filter(q => q.questionId !== questionId)
      .map((q, index) => ({
        ...q,
        order: index + 1, // Reorder questions
      }));

    const questionsWithHeadLogic = calculateHeadQuestions(filteredQuestions);
    const finalQuestions = calculateDefaultNextQuestionIds(
      questionsWithHeadLogic
    );

    // Convert to database format
    const questionsForDB = finalQuestions.map(q => ({
      questionId: q.questionId,
      text: q.text,
      type: q.type as any,
      options: q.options?.map(opt => JSON.stringify(opt)) || [],
      order: q.order,
      conditionalLogic: (q.conditionalLogic || []) as any,
      defaultNextQuestionId: q.defaultNextQuestionId || '',
      isHeadQuestion: q.isHeadQuestion ?? true,
      questionMapping: q.questionMapping || '',
    }));

    // Create new version with updated questions
    const now = new Date().toISOString();
    const newVersionNumber = version.versionNumber + 1;

    const { data: newVersion, errors } =
      await client.models.InterviewVersion.create({
        interviewId,
        versionNumber: newVersionNumber,
        isActive: true,
        createdAt: now,
        questions: questionsForDB,
      });

    if (errors || !newVersion) {
      console.error(
        'Errors creating new version with deleted question:',
        errors
      );
      throw new Error('Failed to delete question');
    }

    // Deactivate previous version
    await client.models.InterviewVersion.update({
      id: version.id,
      isActive: false,
    });

    return true;
  } catch (error) {
    console.error('Error deleting question:', error);
    return false;
  }
};
