// Interview Builder New Types - Optimized Models

export type QuestionType = 'text' | 'radio' | 'select' | 'checkbox';

export type ConditionalType = 'equals' | 'notEquals' | 'contains';

export interface ConditionalLogic {
  conditionType: ConditionalType;
  expectedValue: string;
  showQuestionId: string;
}

export interface QuestionOption {
  id: string;
  label: string;
  value: string;
  nextQuestionId?: string; // For branching logic
}

export interface Question {
  questionId: string;
  text: string;
  type: QuestionType;
  options?: QuestionOption[];
  order: number;
  conditionalLogic?: ConditionalLogic[];
  defaultNextQuestionId?: string; // ID of the next question in the flow
  isHeadQuestion?: boolean; // True for head questions, false for conditional questions
  questionMapping?: string; // Map name for this question
}

export interface InterviewNew {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  versions?: InterviewVersionNew[];
}

export interface InterviewVersionNew {
  id: string;
  interviewId: string;
  versionNumber: number;
  isActive: boolean;
  createdAt: string;
  questions: Question[];
}

export interface InterviewWithLatestVersion {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  latestVersionNumber?: number;
  questionsCount: number;
}

// API Request/Response types
export interface CreateInterviewNewRequest {
  name: string;
  description?: string;
}

export interface UpdateInterviewNewRequest {
  id: string;
  name?: string;
  description?: string;
}

export interface CreateQuestionNewRequest {
  text: string;
  type: QuestionType;
  options?: Omit<QuestionOption, 'id'>[];
  isRequired?: boolean;
  conditionalLogic?: ConditionalLogic[];
  defaultNextQuestionId?: string;
  questionMapping?: string;
}

export interface UpdateQuestionNewRequest extends CreateQuestionNewRequest {
  questionId: string;
}

export interface InterviewListResponse {
  interviews: InterviewWithLatestVersion[];
  total: number;
}

export interface QuestionListResponse {
  questions: Question[];
  total: number;
}

// Form state types for UI components
export interface QuestionFormData {
  text: string;
  type: QuestionType;
  options: QuestionOption[];
  conditionalLogic: ConditionalLogic[];
  defaultNextQuestionId?: string;
  questionMapping?: string;
}

export interface InterviewFormData {
  name: string;
  description: string;
}

// UI State types
export interface QuestionBuilderState {
  currentQuestion: Question | null;
  isEditing: boolean;
  showPreview: boolean;
  validationErrors: Record<string, string>;
}

export interface InterviewBuilderState {
  currentInterview: InterviewNew | null;
  currentVersion: InterviewVersionNew | null;
  activeTab: 'questions' | 'preview';
  isEditing: boolean;
  validationErrors: Record<string, string>;
}

// Validation types
export interface ValidationErrors {
  [key: string]: string;
}

export interface QuestionValidationResult {
  isValid: boolean;
  errors: ValidationErrors;
}

export interface InterviewValidationResult {
  isValid: boolean;
  errors: ValidationErrors;
}
